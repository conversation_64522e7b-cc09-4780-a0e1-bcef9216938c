import sqlite3
import hashlib
import json
from datetime import datetime
import os

DATABASE_PATH = "vastu_ecommerce.db"

def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def init_database():
    """Initialize database with all required tables"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT,
            phone TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            pincode TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Categories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT,
            image_url TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Products table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            category_id INTEGER,
            image_url TEXT,
            stock_quantity INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
    ''')
    
    # Cart table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cart (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER DEFAULT 1,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (product_id) REFERENCES products (id),
            UNIQUE(user_id, product_id)
        )
    ''')
    
    # Orders table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            status TEXT DEFAULT 'pending',
            shipping_address TEXT,
            payment_method TEXT,
            payment_status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Order items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')
    
    # Consultations table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS consultations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            consultation_type TEXT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            duration TEXT,
            status TEXT DEFAULT 'pending',
            scheduled_date DATE,
            scheduled_time TIME,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()

def create_admin_user():
    """Create default admin user"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Check if admin already exists
    cursor.execute("SELECT id FROM users WHERE username = 'admin'")
    if cursor.fetchone():
        conn.close()
        return
    
    # Create admin user
    admin_password = hash_password("admin123")
    cursor.execute('''
        INSERT INTO users (username, email, password_hash, full_name, is_admin)
        VALUES (?, ?, ?, ?, ?)
    ''', ("admin", "<EMAIL>", admin_password, "Administrator", True))
    
    conn.commit()
    conn.close()

def insert_sample_data():
    """Insert sample categories and products"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Check if data already exists
    cursor.execute("SELECT COUNT(*) FROM categories")
    if cursor.fetchone()[0] > 0:
        conn.close()
        return
    
    # Insert categories
    categories = [
        ("Crystals", "Healing crystals and gemstones for Vastu", ""),
        ("Yantras", "Sacred geometric symbols for positive energy", ""),
        ("Books", "Educational books on Vastu Shastra", ""),
        ("Decorative Items", "Vastu-compliant decorative pieces", ""),
        ("Consultation Tools", "Professional Vastu consultation tools", "")
    ]
    
    cursor.executemany('''
        INSERT INTO categories (name, description, image_url)
        VALUES (?, ?, ?)
    ''', categories)
    
    # Insert sample products
    products = [
        ("Crystal Pyramid", "Clear quartz pyramid for energy amplification", 999.00, 1, "", 50),
        ("Rose Quartz Heart", "Rose quartz for love and harmony", 599.00, 1, "", 30),
        ("Amethyst Cluster", "Natural amethyst for spiritual protection", 1299.00, 1, "", 25),
        ("Sri Yantra", "Sacred geometry for prosperity", 799.00, 2, "", 40),
        ("Vastu Purusha Yantra", "Traditional Vastu yantra", 1199.00, 2, "", 20),
        ("Ganesh Yantra", "For removing obstacles", 699.00, 2, "", 35),
        ("Vastu Shastra Complete Guide", "Comprehensive book on Vastu principles", 499.00, 3, "", 100),
        ("Feng Shui and Vastu", "Combined Eastern wisdom", 399.00, 3, "", 75),
        ("Vastu for Modern Homes", "Contemporary Vastu applications", 599.00, 3, "", 60),
        ("Feng Shui Mirror", "Bagua mirror for protection", 799.00, 4, "", 45),
        ("Wind Chimes", "Metal wind chimes for positive energy", 899.00, 4, "", 55),
        ("Laughing Buddha", "For happiness and prosperity", 649.00, 4, "", 70),
        ("Vastu Compass", "Professional direction finder", 1499.00, 5, "", 15),
        ("Energy Meter", "Measure energy levels in spaces", 2999.00, 5, "", 10),
        ("Dowsing Rods", "For detecting energy fields", 899.00, 5, "", 20)
    ]
    
    cursor.executemany('''
        INSERT INTO products (name, description, price, category_id, image_url, stock_quantity)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', products)
    
    conn.commit()
    conn.close()

# User management functions
def create_user(username, email, password, full_name=None):
    """Create a new user"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        password_hash = hash_password(password)
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, full_name)
            VALUES (?, ?, ?, ?)
        ''', (username, email, password_hash, full_name))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return user_id
    except sqlite3.IntegrityError:
        conn.close()
        return None

def authenticate_user(username, password):
    """Authenticate user login"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    password_hash = hash_password(password)
    cursor.execute('''
        SELECT id, username, email, full_name, is_admin
        FROM users 
        WHERE username = ? AND password_hash = ?
    ''', (username, password_hash))
    
    user = cursor.fetchone()
    conn.close()
    
    if user:
        return dict(user)
    return None

def get_user_by_id(user_id):
    """Get user information by ID"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, username, email, full_name, phone, address, city, state, pincode
        FROM users WHERE id = ?
    ''', (user_id,))
    
    user = cursor.fetchone()
    conn.close()
    
    if user:
        return dict(user)
    return None

# Product management functions
def get_all_products(category_id=None, search_term=None):
    """Get all products with optional filtering"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    query = '''
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_active = 1
    '''
    params = []
    
    if category_id:
        query += " AND p.category_id = ?"
        params.append(category_id)
    
    if search_term:
        query += " AND (p.name LIKE ? OR p.description LIKE ?)"
        params.extend([f"%{search_term}%", f"%{search_term}%"])
    
    query += " ORDER BY p.created_at DESC"
    
    cursor.execute(query, params)
    products = cursor.fetchall()
    conn.close()
    
    return [dict(product) for product in products]

def get_product_by_id(product_id):
    """Get product by ID"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.id = ?
    ''', (product_id,))
    
    product = cursor.fetchone()
    conn.close()
    
    if product:
        return dict(product)
    return None

def get_all_categories():
    """Get all categories"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute("SELECT * FROM categories ORDER BY name")
    categories = cursor.fetchall()
    conn.close()
    
    return [dict(category) for category in categories]

# Initialize database when module is imported
if __name__ == "__main__":
    init_database()
    create_admin_user()
    insert_sample_data()
    print("Database initialized successfully!")
